import React, { useState, useContext } from 'react';
import { View, TouchableOpacity, Text } from 'react-native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons, AntDesign } from '@expo/vector-icons';
import Home from './Home';
import Chats from './Chats';
import Profile from './Profile';
import ListingsScreen from './Listings';
import FABModal from '../Components/Home/FabModal';
import '../../global.css';
import { ThemeContext } from '../../context/ThemeContext';
const Tab = createBottomTabNavigator();

// Create a separate component to avoid inline function
const EmptyComponent = () => null;

// Custom multi-line tab label component
const MultiLineTabLabel = ({ focused, color }) => {
    return (
        <View style={{ alignItems: 'center', justifyContent: 'center' }}>
            <Text
                style={{
                    fontSize: 10,
                    fontWeight: '700',
                    color: color,
                    textAlign: 'center',
                    lineHeight: 12,
                }}
            >
                Projects &
            </Text>
            <Text
                style={{
                    fontSize: 10,
                    fontWeight: '700',
                    color: color,
                    textAlign: 'center',
                    lineHeight: 12,
                }}
            >
                Listings
            </Text>
        </View>
    );
};

export default function App() {
    const { theme } = useContext(ThemeContext);
    const [modalVisible, setModalVisible] = useState(false);
    return (
        <>
            <FABModal
                modalVisible={modalVisible}
                setModalVisible={setModalVisible}
            />
            <Tab.Navigator
                initialRouteName="Home"
                screenOptions={() => ({
                    tabBarActiveTintColor: theme.PRIMARY,
                    tabBarInactiveTintColor: theme.GRAY,
                    tabBarStyle: {
                        position: 'absolute',
                        height: 65,
                        paddingTop: 4,
                        paddingBottom: 8,
                        backgroundColor: theme.BACKGROUND,
                        borderTopWidth: 0,
                        elevation: 10,
                        shadowColor: theme.SHADOW,
                        shadowOffset: { width: 0, height: -2 },
                        shadowOpacity: 0.12,
                        shadowRadius: 6,
                        borderRadius: 14,
                        marginHorizontal: 8,
                        marginBottom: 6,
                    },
                    headerShown: false,
                    tabBarLabelStyle: {
                        fontSize: 10,
                        fontWeight: '700',
                        marginTop: 2,
                    },
                })}
            >
                <Tab.Screen
                    name="Home"
                    component={Home}
                    options={{
                        tabBarIcon: ({ color, focused }) => (
                            <Ionicons
                                name={focused ? 'home' : 'home-outline'}
                                size={24}
                                color={color}
                                style={
                                    focused
                                        ? { transform: [{ scale: 1.15 }] }
                                        : {}
                                }
                            />
                        ),
                        tabBarLabel: 'Home',
                    }}
                />

                <Tab.Screen
                    name="Listings"
                    component={ListingsScreen}
                    options={{
                        tabBarIcon: ({ color, focused }) => (
                            <Ionicons
                                name={focused ? 'folder' : 'folder-outline'}
                                size={28}
                                color={color}
                                style={
                                    focused
                                        ? { transform: [{ scale: 1.15 }] }
                                        : {}
                                }
                            />
                        ),
                        tabBarLabel: ({ focused, color }) => (
                            <MultiLineTabLabel
                                focused={focused}
                                color={color}
                            />
                        ),
                    }}
                />

                <Tab.Screen
                    name="Add"
                    component={EmptyComponent}
                    options={{
                        tabBarButton: (props) => (
                            <View
                                style={{
                                    top: -1,
                                    left: 9,
                                    right: 9,
                                    backgroundColor: theme.BORDER,
                                    borderWidth: 2,
                                    borderColor: theme.PRIMARY,
                                    width: '48',
                                    height: '48',
                                    ...props.style,
                                }}
                                className="rounded-full justify-center items-center shadow-fab absolute align-center"
                            >
                                <TouchableOpacity
                                    className=""
                                    activeOpacity={0.8}
                                    onPress={() => setModalVisible(true)}
                                >
                                    <AntDesign
                                        name="pluscircle"
                                        size={42}
                                        color={theme.PRIMARY}
                                    />
                                </TouchableOpacity>
                            </View>
                        ),
                    }}
                />

                <Tab.Screen
                    name="Chats"
                    component={Chats}
                    options={{
                        tabBarIcon: ({ color, focused }) => (
                            <Ionicons
                                name={
                                    focused
                                        ? 'chatbubble-ellipses'
                                        : 'chatbubble-ellipses-outline'
                                }
                                size={24}
                                color={color}
                                style={
                                    focused
                                        ? { transform: [{ scale: 1.15 }] }
                                        : {}
                                }
                            />
                        ),
                        tabBarLabel: 'Chats',
                    }}
                />

                <Tab.Screen
                    name="Profile"
                    component={Profile}
                    options={{
                        tabBarIcon: ({ color, focused }) => (
                            <Ionicons
                                name={focused ? 'person' : 'person-outline'}
                                size={24}
                                color={color}
                                style={
                                    focused
                                        ? { transform: [{ scale: 1.15 }] }
                                        : {}
                                }
                            />
                        ),
                        tabBarLabel: 'Profile',
                    }}
                />
            </Tab.Navigator>
        </>
    );
}
